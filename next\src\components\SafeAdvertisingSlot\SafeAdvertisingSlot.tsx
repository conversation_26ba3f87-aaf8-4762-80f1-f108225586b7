import React from 'react';
import { AdvertisingSlot } from 'react-advertising';
import AdvertisingErrorBoundary from '../AdvertisingErrorBoundary/AdvertisingErrorBoundary';

interface SafeAdvertisingSlotProps {
  id: string;
  className?: string;
  viewportsEnabled?: {
    mobile?: boolean;
    tablet?: boolean;
    desktop?: boolean;
  };
  [key: string]: any; // Allow other props to be passed through
}

/**
 * A safe wrapper around AdvertisingSlot that prevents advertising errors
 * from crashing the application
 */
const SafeAdvertisingSlot: React.FC<SafeAdvertisingSlotProps> = (props) => {
  return (
    <AdvertisingErrorBoundary
      fallback={
        <div 
          className={props.className} 
          style={{ 
            display: 'block',
            visibility: 'hidden',
            // Maintain the layout space but hide the content
          }}
          aria-hidden="true"
        />
      }
    >
      <AdvertisingSlot {...props} />
    </AdvertisingErrorBoundary>
  );
};

export default SafeAdvertisingSlot;
