import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary specifically for advertising components
 * Prevents advertising errors from crashing the entire application
 */
class AdvertisingErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log advertising errors but don't throw them
    console.warn('Advertising component error (non-critical):', error, errorInfo);
    
    // You could also send this to an error reporting service
    // but make sure it doesn't throw errors itself
    try {
      // Optional: Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorInfo });
    } catch (reportingError) {
      console.warn('Error reporting failed:', reportingError);
    }
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI or nothing at all
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Return empty div to maintain layout
      return <div className="advertising-error-fallback" style={{ display: 'none' }} />;
    }

    return this.props.children;
  }
}

export default AdvertisingErrorBoundary;
